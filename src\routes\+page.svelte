<script lang="ts">
	// No imports needed - using HTML elements with Tailwind/Skeleton classes
</script>

<svelte:head>
	<title>Home | Borders Electrical</title>
	<meta name="description" content="Placeholder homepage for Borders Electrical" />
</svelte:head>

<section class="flex flex-col items-center justify-center min-h-screen p-4 bg-background text-foreground">
	<div class="max-w-md w-full bg-white rounded-lg shadow-lg border border-gray-200">
		<div class="p-6 text-center space-y-4">
			<h1 class="text-4xl font-bold">Welcome to Borders Electrical</h1>
			<p>This homepage is using the Skeleton UI framework.</p>
			<button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
				Skeleton is working ✅
			</button>
		</div>
	</div>
</section>
