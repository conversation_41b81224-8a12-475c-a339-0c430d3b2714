<script lang="ts">
	import { <PERSON><PERSON>, <PERSON>, CardContent } from '@skeletonlabs/skeleton';
</script>

<svelte:head>
	<title>Home | Borders Electrical</title>
	<meta name="description" content="Placeholder homepage for Borders Electrical" />
</svelte:head>

<section class="flex flex-col items-center justify-center min-h-screen p-4 bg-background text-foreground">
	<Card class="max-w-md w-full">
		<CardContent class="text-center space-y-4">
			<h1 class="text-4xl font-bold">Welcome to Borders Electrical</h1>
			<p>This homepage is using the Skeleton UI framework.</p>
			<Button>Skeleton is working ✅</Button>
		</CardContent>
	</Card>
</section>
